# Quest Notify System Implementation

## Genel Bakış
Bu implementasyon Sky Game Network sunucusundan quest notify sistemini 1453 Farm sunucusuna entegre eder.

## <PERSON><PERSON><PERSON><PERSON>

### Client Tarafı
1. **<PERSON>re Klasörü Oluşturuldu**: `1453 Farm Client - Ko-Yardim/Lacre/`
2. **Do<PERSON>alar Kopyalandı**:
   - `co_quest_mini_tip.sgn` - Ana quest notification sistemi
   - `co_quest_viewer.sgn` - Quest görüntüleme penceresi
   - `co_page_quest.sgn` - Quest sayfa sistemi
   - `co_quest_completed.sgn` - Quest tamamlama bildirimi
   - `co_quest_map.sgn` - Quest harita sistemi
   - `co_quest_npc_menu.sgn` - Quest NPC menü sistemi
   - `co_quest_requital.sgn` - Quest ödül sistemi

### Server Tarafı

#### 1. Packet Tanımı (`shared/packets.h`)
```cpp
#define WIZ_QUEST_NOTIFY    0x95 // quest notification system
```

#### 2. User Header (`GameServer/User.h`)
```cpp
// Quest Notify System
void QuestNotifyHandler(Packet & pkt);
void SendQuestNotify(uint32 questID, std::string questName, uint8 notifyType = 1);
void SendQuestMiniTip(uint32 questID, std::string questName, std::string questDesc);
```

#### 3. Packet Handler (`GameServer/User.cpp`)
```cpp
case WIZ_QUEST_NOTIFY:
    QuestNotifyHandler(pkt);
    break;
```

#### 4. Quest Handler Implementation (`GameServer/QuestHandler.cpp`)
- `QuestNotifyHandler()` - Packet işleme fonksiyonu
- `SendQuestNotify()` - Quest bildirimi gönderme
- `SendQuestMiniTip()` - Quest mini tip gönderme
- Quest verme ve tamamlama fonksiyonlarına notify eklendi

## Kullanım

### Quest Verme
Quest verildiğinde otomatik olarak notification gönderilir:
```cpp
SendQuestNotify(questID, questName, 1); // Type 1: New quest
```

### Quest Tamamlama
Quest tamamlandığında otomatik olarak notification gönderilir:
```cpp
SendQuestNotify(questID, questName, 2); // Type 2: Completed quest
```

### Manuel Kullanım
```cpp
// Quest notification göster
user->SendQuestNotify(1001, "Kill 10 Orcs", 1);

// Quest mini tip göster
user->SendQuestMiniTip(1001, "Kill 10 Orcs", "Hunt orcs in the forest");
```

## Packet Protokolü

### WIZ_QUEST_NOTIFY (0x95)

#### Opcode 1: Quest Notification
```
Client -> Server: [0x95][0x01][questID][questName]
Server -> Client: [0x95][0x01][questID][questName][notifyType]
```

#### Opcode 2: Quest Mini Tip
```
Client -> Server: [0x95][0x02][questID][questName][questDesc]
Server -> Client: [0x95][0x02][questID][questName][questDesc]
```

## Test Etme

1. **Server Derleme**: Visual Studio ile server'ı derleyin
2. **Client Test**: Oyuna giriş yapın ve quest alın
3. **Notification Kontrolü**: Quest notification penceresinin görünüp görünmediğini kontrol edin

## Sorun Giderme

### Quest Notify Görünmüyor
- Lacre klasörünün doğru yerde olduğunu kontrol edin
- .sgn dosyalarının doğru kopyalandığını kontrol edin
- Client'ı yeniden başlatın

### Server Crash
- Packet handler'ların doğru eklendiğini kontrol edin
- String formatının doğru olduğunu kontrol edin
- Server log'larını kontrol edin

### Client Crash
- .sgn dosyalarının bozuk olmadığını kontrol edin
- Lacre klasörünün client ana dizininde olduğunu kontrol edin

## Gelişmiş Özellikler

### Database Entegrasyonu (Opsiyonel)
Quest notify ayarları için database tablosu:
```sql
CREATE TABLE QUEST_NOTIFY_SETTINGS (
    QuestID INT PRIMARY KEY,
    NotifyEnabled TINYINT DEFAULT 1,
    NotifyMessage VARCHAR(255),
    NotifyDuration INT DEFAULT 5000
);
```

### Özelleştirme
- Notification süresini ayarlama
- Farklı quest türleri için farklı notification stilleri
- Ses efektleri ekleme
- Pozisyon ayarlama

## Notlar
- Bu sistem Sky Game Network'ten alınmış ve 1453 Farm'a uyarlanmıştır
- .sgn dosyaları binary format'tadır ve düzenlenemez
- Sistem hem quest verme hem de quest tamamlama için çalışır
- Client-server iletişimi WIZ_QUEST_NOTIFY packet'i ile sağlanır
