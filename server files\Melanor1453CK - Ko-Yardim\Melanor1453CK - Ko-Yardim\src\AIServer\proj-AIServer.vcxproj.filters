﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{369fe4a2-2b16-4ee3-aa20-f8f7e17f2310}</UniqueIdentifier>
      <Extensions>cpp;c;cxx;rc;def;r;odl;idl;hpj;bat</Extensions>
    </Filter>
    <Filter Include="Resource Files">
      <UniqueIdentifier>{a28bbc89-f194-4df4-a771-852c2fa9c91c}</UniqueIdentifier>
    </Filter>
    <Filter Include="N3Base">
      <UniqueIdentifier>{5056e633-713a-4c87-981f-339526193987}</UniqueIdentifier>
    </Filter>
    <Filter Include="Database">
      <UniqueIdentifier>{20471dba-a07d-44f4-8d18-1dfea6801a6c}</UniqueIdentifier>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{28f89997-9b80-4a68-8e92-b328a184b607}</UniqueIdentifier>
      <Extensions>h;hpp;hxx;hm;inl</Extensions>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="MAP.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Npc.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="NpcMagicProcess.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="NpcThread.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Party.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="PathFind.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="RoomEvent.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ServerDlg.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\N3BASE\N3ShapeMgr.cpp">
      <Filter>N3Base</Filter>
    </ClCompile>
    <ClCompile Include="GameSocket.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="main.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="StdAfx.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="AIUser.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\GameServer\Unit.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\GameServer\MagicProcess.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="Define.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Extern.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="MAP.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Npc.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="NpcMagicProcess.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="NpcTable.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="NpcThread.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Party.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="PathFind.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Region.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="RoomEvent.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ServerDlg.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="StdAfx.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="User.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Resource.h">
      <Filter>Resource Files</Filter>
    </ClInclude>
    <ClInclude Include="..\N3BASE\N3ShapeMgr.h">
      <Filter>N3Base</Filter>
    </ClInclude>
    <ClInclude Include="..\N3BASE\My_3DStruct.h">
      <Filter>N3Base</Filter>
    </ClInclude>
    <ClInclude Include="..\shared\database\MakeWeaponTableSet.h">
      <Filter>Database</Filter>
    </ClInclude>
    <ClInclude Include="..\shared\database\NpcItemSet.h">
      <Filter>Database</Filter>
    </ClInclude>
    <ClInclude Include="..\shared\database\NpcPosSet.h">
      <Filter>Database</Filter>
    </ClInclude>
    <ClInclude Include="..\shared\database\NpcTableSet.h">
      <Filter>Database</Filter>
    </ClInclude>
    <ClInclude Include="..\shared\database\ZoneInfoSet.h">
      <Filter>Database</Filter>
    </ClInclude>
    <ClInclude Include="..\shared\database\MagicTableSet.h">
      <Filter>Database</Filter>
    </ClInclude>
    <ClInclude Include="..\shared\database\MagicType1Set.h">
      <Filter>Database</Filter>
    </ClInclude>
    <ClInclude Include="..\shared\database\MagicType2Set.h">
      <Filter>Database</Filter>
    </ClInclude>
    <ClInclude Include="..\shared\database\MakeDefensiveTableSet.h">
      <Filter>Database</Filter>
    </ClInclude>
    <ClInclude Include="..\shared\database\MakeGradeItemTableSet.h">
      <Filter>Database</Filter>
    </ClInclude>
    <ClInclude Include="..\shared\database\MakeLareItemTableSet.h">
      <Filter>Database</Filter>
    </ClInclude>
    <ClInclude Include="GameSocket.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\shared\database\MakeItemGroupSet.h">
      <Filter>Database</Filter>
    </ClInclude>
    <ClInclude Include="..\shared\database\ObjectPosSet.h">
      <Filter>Database</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="RES\AIServer.rc">
      <Filter>Resource Files</Filter>
    </ResourceCompile>
  </ItemGroup>
  <ItemGroup>
    <Image Include="RES\AIServer.ico">
      <Filter>Resource Files</Filter>
    </Image>
  </ItemGroup>
</Project>