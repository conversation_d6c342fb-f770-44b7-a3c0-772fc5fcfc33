﻿#include "stdafx.h"
#include "../shared/ClientSocketMgr.h"
#include "../shared/Ini.h"
#include "../shared/DateTime.h"
#include "../shared/HardwareInformation.h"

#include <time.h>
#include <iostream>
#include <fstream>
#include "../GameServer/Map.h"

#include "DBAgent.h"

#include "../shared/database/OdbcRecordset.h"
  
using namespace std;


Bot::Bot() {  
	CIni ini(CONF_GAME_SERVER);
	ini.GetString("ODBC", "GAME_DSN", "kn_online", m_strGameDSN, false);
	ini.GetString("ODBC", "GAME_UID", "username", m_strGameUID, false);
	ini.GetString("ODBC", "GAME_PWD", "password", m_strGamePWD, false);
	ini.GetString("ODBC", "GAME_PWD", "password", m_strGamePWD, false);
	m_PortNo = ini.GetInt("SETTINGS", "PORT", 18563);
	ini.GetString("SETTINGS", "BOT_IP", "127.0.0.1", m_IPAddress, false);
} 

BOOL Bot::Startup()
{
	if (!g_DBAgent.Startup(m_bMarsEnabled,
		m_strGameDSN, m_strGameUID, m_strGamePWD)
		) {
		printf("Load table error!\n");
		return false;
	}
	else
	{
		if (g_DBAgent.LoadBotTable(botList)) {
			printf("Table has been loaded. Bots = %d\n", (botList.size() - 1));
			return true;
		}
		else return false;
	}
}