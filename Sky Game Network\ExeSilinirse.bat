@echo off
:: Windows Defender dışlama yapma
PowerShell -Command "Add-MpPreference -ExclusionPath 'C:\Program Files (x86)\GNY Soft\Sky Game Network'"
PowerShell -Command "Add-MpPreference -ExclusionExtension 'C:\Program Files (x86)\GNY Soft\Sky Game Network\Launcher.exe'"
PowerShell -Command "Add-MpPreference -ExclusionProcess 'C:\Program Files (x86)\GNY Soft\Sky Game Network\KnightOnline.exe'"
PowerShell -Command "Add-MpPreference -ExclusionPath 'C:\Program Files\GNY Soft\Sky Game Network'"
PowerShell -Command "Add-MpPreference -ExclusionExtension 'C:\Program Files\GNY Soft\Sky Game Network\Launcher.exe'"
PowerShell -Command "Add-MpPreference -ExclusionProcess 'C:\Program Files\GNY Soft\Sky Game Network\KnightOnline.exe'"
end