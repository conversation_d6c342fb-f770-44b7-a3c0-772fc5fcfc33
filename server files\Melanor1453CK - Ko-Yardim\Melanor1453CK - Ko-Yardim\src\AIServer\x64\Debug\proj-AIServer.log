﻿Build started 16.02.2016 19:12:46.
     1>Project "C:\Users\<USER>\Desktop\Melanor1453\melanor1453\AIServer\proj-AIServer.vcxproj" on node 2 (Build target(s)).
     1>ClCompile:
         C:\Program Files (x86)\Microsoft Visual Studio 14.0\VC\bin\x86_amd64\CL.exe /c /nologo /W3 /WX- /MP /O2 /Ob2 /Ot /Oy /GT /GL /D "WIN32" /D "AI_SERVER" /D "_WINDOWS" /D _3DSERVER /D _REPENT /D "_CRT_SECURE_NO_WARNINGS" /D "_WINSOCK_DEPRECATED_NO_WARNINGS" /D _VC80_UPGRADE=0x0600 /D _MBCS /GF /Gm- /EHa /MT /GS- /guard:cf /Qpar /fp:precise /fp:except /Zc:wchar_t /Zc:forScope /Zc:inline /Yc"stdafx.h" /Fp".\Debug/Server.pch" /Fo".\Debug/" /Fd".\Debug/vc140.pdb" /Gd /TP /errorReport:prompt StdAfx.cpp
         StdAfx.cpp
         C:\Program Files (x86)\Microsoft Visual Studio 14.0\VC\bin\x86_amd64\CL.exe /c /nologo /W3 /WX- /MP /O2 /Ob2 /Ot /Oy /GT /GL /D "WIN32" /D "AI_SERVER" /D "_WINDOWS" /D _3DSERVER /D _REPENT /D "_CRT_SECURE_NO_WARNINGS" /D "_WINSOCK_DEPRECATED_NO_WARNINGS" /D _VC80_UPGRADE=0x0600 /D _MBCS /GF /Gm- /EHa /MT /GS- /guard:cf /Qpar /fp:precise /fp:except /Zc:wchar_t /Zc:forScope /Zc:inline /Yu"stdafx.h" /Fp".\Debug/Server.pch" /Fo".\Debug/" /Fd".\Debug/vc140.pdb" /Gd /TP /errorReport:prompt ..\GameServer\MagicProcess.cpp ..\GameServer\Unit.cpp GameSocket.cpp main.cpp MAP.cpp Npc.cpp NpcMagicProcess.cpp NpcThread.cpp Party.cpp PathFind.cpp RoomEvent.cpp ServerDlg.cpp "AIUser.cpp"
         MagicProcess.cpp
         Unit.cpp
         GameSocket.cpp
         main.cpp
     1>C:\Program Files (x86)\Windows Kits\8.1\Include\um\dbghelp.h(1544): warning C4091: 'typedef ': ignored on left of '' when no variable is declared (compiling source file main.cpp)
     1>C:\Program Files (x86)\Windows Kits\8.1\Include\um\dbghelp.h(3190): warning C4091: 'typedef ': ignored on left of '' when no variable is declared (compiling source file main.cpp)
     1>..\GameServer\Unit.cpp(625): warning C4267: 'initializing': conversion from 'size_t' to 'int', possible loss of data
     1>..\GameServer\Unit.cpp(631): warning C4267: 'initializing': conversion from 'size_t' to 'int', possible loss of data
     1>..\GameServer\Unit.cpp(710): warning C4267: 'initializing': conversion from 'size_t' to 'int', possible loss of data
     1>..\GameServer\Unit.cpp(716): warning C4267: 'initializing': conversion from 'size_t' to 'int', possible loss of data
         MAP.cpp
         Npc.cpp
         NpcMagicProcess.cpp
     1>Npc.cpp(535): warning C4244: 'return': conversion from 'float' to 'time_t', possible loss of data
         NpcThread.cpp
     1>c:\users\<USER>\desktop\melanor1453\melanor1453\aiserver\../shared/STLMap.h(16): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data (compiling source file MAP.cpp)
         c:\users\<USER>\desktop\melanor1453\melanor1453\aiserver\../shared/STLMap.h(14): note: while compiling class template member function 'int CSTLMap<CRoomEvent>::GetSize(void)' (compiling source file MAP.cpp)
         MAP.cpp(417): note: see reference to function template instantiation 'int CSTLMap<CRoomEvent>::GetSize(void)' being compiled
         c:\users\<USER>\desktop\melanor1453\melanor1453\aiserver\MAP.h(42): note: see reference to class template instantiation 'CSTLMap<CRoomEvent>' being compiled (compiling source file MAP.cpp)
         Party.cpp
         PathFind.cpp
         RoomEvent.cpp
         ServerDlg.cpp
         AIUser.cpp
     1>c:\users\<USER>\desktop\melanor1453\melanor1453\aiserver\../shared/STLMap.h(16): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data (compiling source file RoomEvent.cpp)
         c:\users\<USER>\desktop\melanor1453\melanor1453\aiserver\../shared/STLMap.h(14): note: while compiling class template member function 'int CSTLMap<int>::GetSize(void)' (compiling source file RoomEvent.cpp)
         RoomEvent.cpp(228): note: see reference to function template instantiation 'int CSTLMap<int>::GetSize(void)' being compiled
         c:\users\<USER>\desktop\melanor1453\melanor1453\aiserver\RoomEvent.h(55): note: see reference to class template instantiation 'CSTLMap<int>' being compiled (compiling source file RoomEvent.cpp)
     1>ServerDlg.cpp(629): warning C4267: 'initializing': conversion from 'size_t' to 'uint32', possible loss of data
     1>c:\users\<USER>\desktop\melanor1453\melanor1453\aiserver\../shared/STLMap.h(16): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data (compiling source file ServerDlg.cpp)
         c:\users\<USER>\desktop\melanor1453\melanor1453\aiserver\../shared/STLMap.h(14): note: while compiling class template member function 'int CSTLMap<_NPC_LIVE_TIME>::GetSize(void)' (compiling source file ServerDlg.cpp)
         ServerDlg.cpp(865): note: see reference to function template instantiation 'int CSTLMap<_NPC_LIVE_TIME>::GetSize(void)' being compiled
         c:\users\<USER>\desktop\melanor1453\melanor1453\aiserver\ServerDlg.h(122): note: see reference to class template instantiation 'CSTLMap<_NPC_LIVE_TIME>' being compiled (compiling source file ServerDlg.cpp)
         C:\Program Files (x86)\Microsoft Visual Studio 14.0\VC\bin\x86_amd64\CL.exe /c /nologo /W3 /WX- /MP /O2 /Ob2 /Ot /Oy /GT /GL /D "WIN32" /D "AI_SERVER" /D "_WINDOWS" /D _3DSERVER /D _REPENT /D "_CRT_SECURE_NO_WARNINGS" /D "_WINSOCK_DEPRECATED_NO_WARNINGS" /D _VC80_UPGRADE=0x0600 /D _MBCS /GF /Gm- /EHa /MT /GS- /guard:cf /Qpar /fp:precise /fp:except /Zc:wchar_t /Zc:forScope /Zc:inline /Fo".\Debug/" /Fd".\Debug/vc140.pdb" /Gd /TP /errorReport:prompt ..\N3BASE\N3ShapeMgr.cpp
         N3ShapeMgr.cpp
       ResourceCompile:
         C:\Program Files (x86)\Windows Kits\8.1\bin\x86\rc.exe /D _DEBUG /D _VC80_UPGRADE=0x0600 /l"0x0000" /nologo /fo"x64\Debug\AIServer.res" "RES\AIServer.rc" 
       Link:
         C:\Program Files (x86)\Microsoft Visual Studio 14.0\VC\bin\x86_amd64\link.exe /ERRORREPORT:QUEUE /OUT:"C:\Users\<USER>\Desktop\Melanor1453\melanor1453\x64\Debug\AIServer.exe" /INCREMENTAL:NO /NOLOGO ws2_32.lib crypt32.lib C:\Users\<USER>\Desktop\Melanor1453\melanor1453\../bin/Debug/shared.lib C:\Users\<USER>\Desktop\Melanor1453\melanor1453\../bin/Debug/ChilkatDbg.lib kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /MANIFEST /MANIFESTUAC:"level='requireAdministrator' uiAccess='false'" /manifest:embed /PDB:"C:\Users\<USER>\Desktop\Melanor1453\melanor1453\x64\Debug\AIServer.pdb" /ASSEMBLYDEBUG:DISABLE /LTCG:incremental /TLBID:1 /DYNAMICBASE:NO /IMPLIB:"C:\Users\<USER>\Desktop\Melanor1453\melanor1453\x64\Debug\AIServer.lib" /MACHINE:X64 /CLRSupportLastError /SAFESEH /guard:cf /STACK:100 "x64\Debug\AIServer.res"
         .\Debug/MagicProcess.obj
         .\Debug/Unit.obj
         .\Debug/N3ShapeMgr.obj
         .\Debug/GameSocket.obj
         .\Debug/main.obj
         .\Debug/MAP.obj
         .\Debug/Npc.obj
         .\Debug/NpcMagicProcess.obj
         .\Debug/NpcThread.obj
         .\Debug/Party.obj
         .\Debug/PathFind.obj
         .\Debug/RoomEvent.obj
         .\Debug/ServerDlg.obj
         .\Debug/StdAfx.obj
         ".\Debug/AIUser.obj"
     1>LINK : fatal error LNK1246: '/SAFESEH' not compatible with 'x64' target machine; link without '/SAFESEH'
     1>Done Building Project "C:\Users\<USER>\Desktop\Melanor1453\melanor1453\AIServer\proj-AIServer.vcxproj" (Build target(s)) -- FAILED.

Build FAILED.

Time Elapsed 00:00:09.42
