Quest Notify System Test Guide
=====================================

Bu dosya quest notify sisteminin test edilmesi için hazırlanmıştır.

KURULUM ADIMLAR:
1. <PERSON>re klasöründeki tüm .sgn dosyaları client'a kopyalandı
2. Server tarafında WIZ_QUEST_NOTIFY packet'i eklendi (0x95)
3. QuestHandler.cpp'ye quest notify fonksiyonları eklendi
4. User.h ve User.cpp'ye gerekli handler'lar eklendi

TEST ETME:
1. Server'ı derleyin ve çalıştırın
2. Client'ı başlatın
3. Bir NPC'den quest alın
4. Quest notify penceresinin görünüp görünmediğini kontrol edin

DOSYA YAPISI:
- co_quest_mini_tip.sgn: Ana quest notification penceresi
- co_quest_viewer.sgn: Quest görüntüleme penceresi
- co_page_quest.sgn: Quest sayfa sistemi
- co_quest_completed.sgn: Quest tamamlama bildirimi
- co_quest_map.sgn: Quest harita sistemi
- co_quest_npc_menu.sgn: Quest NPC menü sistemi
- co_quest_requital.sgn: Quest ödül sistemi

PACKET PROTOKOLÜ:
WIZ_QUEST_NOTIFY (0x95)
- Opcode 1: Quest notification göster
- Opcode 2: Quest mini tip göster

SORUN GİDERME:
- Eğer quest notify görünmüyorsa Lacre klasörünün doğru yerde olduğunu kontrol edin
- Server log'larında hata mesajları olup olmadığını kontrol edin
- Client crash oluyorsa .sgn dosyalarının doğru kopyalandığını kontrol edin
