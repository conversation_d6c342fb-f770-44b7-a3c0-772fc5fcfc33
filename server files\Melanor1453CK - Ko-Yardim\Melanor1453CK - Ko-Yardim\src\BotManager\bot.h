#pragma once
  
#include "../GameServer/resource.h" 

#include "../GameServer/Define.h" 
 
class C3DMap;
class CUser;

#include "../GameServer/LoadServerData.h"

#include "../GameServer/User.h" 
#include "../shared/ClientSocketMgr.h"

typedef std::map<std::string, CUser *>	NameMap;

struct _BOT_TYPE
{
	std::string strAccountID;
	std::string strPasswd;
	std::string strUserID;
	uint16 ID;
};
  

typedef CSTLMap <_BOT_TYPE>	BotTypeArray;

class Bot
{
public:
	Bot();   
	BOOL Startup();
	std::vector<_BOT_TYPE> botList;
	int m_PortNo;
	std::string m_IPAddress;

private:
	std::string m_strGameDSN, m_strAccountDSN;
	std::string m_strGameUID, m_strAccountUID;
	std::string m_strGamePWD, m_strAccountPWD; 
	bool m_bMarsEnabled;
	BotTypeArray	m_BotArray;
};
