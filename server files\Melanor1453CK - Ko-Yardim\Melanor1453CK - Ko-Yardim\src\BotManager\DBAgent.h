#pragma once

#include "../shared/database/OdbcConnection.h"

enum UserUpdateType
{
	UPDATE_LOGOUT,
	UPDATE_ALL_SAVE,
	UPDATE_PACKET_SAVE,
};

enum RentalType
{
	RENTAL_TYPE_IN_LIST		= 1,
	RENTAL_TYPE_LENDER		= 2,
	RENTAL_TYPE_BORROWER	= 3
};

struct _USER_RENTAL_ITEM
{
	std::string strUserID;
	uint64 nSerialNum;
	uint32 nRentalIndex, nItemID, nRentalMoney;
	uint16 sDurability, sRentalTime;
	int16 sMinutesRemaining;
	uint8 byRentalType, byRegType;
	char szTimeRental[30];

	_USER_RENTAL_ITEM()
	{
		memset(&szTimeRental, 0, sizeof(szTimeRental));
	}
};

typedef std::map<uint64, _USER_RENTAL_ITEM *> UserRentalMap;

class Packet;
class CUser;
struct _ITEM_DATA;
class CDBAgent  
{
public:
	CDBAgent();


	bool Startup(bool bMarsEnabled, 
		tstring & strGameDSN, tstring & strGameUID, tstring & strGamePWD);

	bool Connect(bool bMarsEnabled, 
		tstring & strGameDSN, tstring & strGameUID, tstring & strGamePWD);

	void ReportSQLError(OdbcError *pError);
	bool LoadBotTable(std::vector<_BOT_TYPE> & botList);
	 
	~CDBAgent();

private:
	OdbcConnection *m_GameDB, *m_AccountDB;
	std::recursive_mutex m_lock; 
};

extern CDBAgent g_DBAgent;