﻿#include "stdafx.h"
#include "../shared/Condition.h"
#include "ConsoleInputThread.h"
#include "../shared/signal_handler.h" 
#include "string.h"

static Condition s_hEvent;

BOOL WINAPI _ConsoleHandler(DWORD dwCtrlType);

std::string SendPackets(KOSocket * kSoc, Packet * pkt, Socket * soc);

bool g_bRunning = true; 

Bot * g_Bot;

bool ConnectToHost(int PortNo, const char* IPAddress, SOCKET &s)
{
	//Start up Winsock…
	WSADATA wsadata;

	int error = WSAStartup(0x0202, &wsadata);

	//Did something happen?
	if (error)
		return false;

	//Did we get the right Winsock version?
	if (wsadata.wVersion != 0x0202)
	{
		WSACleanup(); //Clean up Winsock
		return false;
	}

	//Fill out the information needed to initialize a socket…
	SOCKADDR_IN target; //Socket address information

	target.sin_family = AF_INET; // address family Internet
	target.sin_port = htons(PortNo); //Port to connect on
	target.sin_addr.s_addr = inet_addr(IPAddress); //Target IP

	s = socket(AF_INET, SOCK_STREAM, IPPROTO_TCP); //Create socket
	if (s == INVALID_SOCKET)
	{
		return false; //Couldn't create the socket
	}

	//Try connecting...

	if (connect(s, (SOCKADDR *)&target, sizeof(target)) == SOCKET_ERROR)
	{
		return false; //Couldn't connect
	}
	else
		return true; //Success
	 
}

int main()
{
	SetConsoleTitle("Clientless - Toma v" STRINGIFY(__VERSION));
	// Override the console handler
	SetConsoleCtrlHandler(_ConsoleHandler, TRUE);

	HookSignals(&s_hEvent);

	// Start up the time updater thread
	//StartTimeThread();

	g_Bot = new Bot();
	 
	if (g_Bot->Startup()) {
		printf("Server Connection IP = %s, Port = %d\n", g_Bot->m_IPAddress.c_str(), g_Bot->m_PortNo);
		for each (_BOT_TYPE bot in g_Bot->botList)
		{
			if (bot.ID != 0)
				Thread * _th = new Thread(BotProcess, static_cast<void *>(&bot));
		}
	}

	// Start up the console input thread 

	s_hEvent.Wait();
	g_bRunning = false;
	CleanupTimeThread(); 
	UnhookSignals();
	 
    return 0;
}

BOOL WINAPI _ConsoleHandler(DWORD dwCtrlType)
{
	s_hEvent.BeginSynchronized();
	s_hEvent.Signal();
	s_hEvent.EndSynchronized();
	sleep(10000); // Win7 onwards allows 10 seconds before it'll forcibly terminate
	return TRUE;
}

uint32 THREADCALL BotProcess(void * pVoid) {
	_BOT_TYPE Type = *reinterpret_cast<_BOT_TYPE*>(pVoid);
	bool isLogin = false;
	bool isServerList = false;
	bool isCharacterSelect = false;
	bool isGame = false;
	bool isSocket = false; 
	SOCKET *_Socket = new SOCKET(); 
	Socket *_soc = nullptr;
	KOSocket * _kSocket;
	while (g_bRunning) {

		if (!isSocket) {
			isSocket = ConnectToHost(g_Bot->m_PortNo, g_Bot->m_IPAddress.c_str(), *_Socket); 
			_soc = new Socket(*_Socket, 1000, 1000);  
			_kSocket = (KOSocket *)(&_soc);  
		//	_kSocket->EnableCrypto(); 
			printf("Bot Connected -> Soc ID = %d, AccID = %s, Bot ID = %s\n", _kSocket->GetSocketID(), Type.strAccountID.c_str(),Type.strUserID.c_str());
			//send(*_Socket, Type.strAccountID.c_str(), Type.strAccountID.length(),0);
		}
		  
		if (!isServerList) {
			isServerList = true;
			 
			Packet result(WIZ_LOGIN);
			result << Type.strAccountID.c_str() << Type.strPasswd.c_str();

			std::string buff = SendPackets(_kSocket, &result, _soc); 
			send(*_Socket, buff.c_str(), buff.length(), 1); 

			//SendPackets(_kSocket, &result, _soc);
			//_kSocket->Send(&result);
			//_soc->Send((uint8*)result.GetOpcode(), result.size());
		} 

		if (!isCharacterSelect)
		{
			isCharacterSelect = true;
			Packet result(WIZ_ALLCHAR_INFO_REQ); 
			std::string buff = SendPackets(_kSocket, &result, _soc);
			send(*_Socket, buff.c_str(), buff.length(), 1);
			
			Packet result2(WIZ_SEL_CHAR);
			result2 << Type.strAccountID.c_str() << Type.strUserID.c_str() << (uint8)1;
			std::string buff2 = SendPackets(_kSocket, &result2, _soc);
			send(*_Socket, buff2.c_str(), buff2.length(), 1); 
		}
		if (!isGame) {
			isGame = true;

			Packet result(WIZ_GAMESTART);
			result << (uint8)1;
			Packet result2(WIZ_GAMESTART);
			result2 << (uint8)2;
			std::string buff = SendPackets(_kSocket, &result, _soc);
			send(*_Socket, buff.c_str(), buff.length(), 1);
			std::string buff2 = SendPackets(_kSocket, &result2, _soc);
			send(*_Socket, buff2.c_str(), buff2.length(), 1);
		//	SendPackets(_kSocket, &result, _soc);
		//	SendPackets(_kSocket, &result2, _soc);
		}
	}
	return 0;
}

std::string SendPackets(KOSocket * kSoc,Packet * pkt,Socket * soc)
{  
	bool r;

	uint8 opcode = pkt->GetOpcode();
	uint8 * out_stream = nullptr;
	uint16 len = (uint16)(pkt->size() + 1);

	if (kSoc->isCryptoEnabled())
	{
		len += 5;

		out_stream = new uint8[len];

		*(uint16 *)&out_stream[0] = 0x1efc;
		*(uint16 *)&out_stream[2] = (uint16)(kSoc->m_sequence); // this isn't actually incremented here
		out_stream[4] = 0;
		out_stream[5] = pkt->GetOpcode();

		if (pkt->size() > 0)
			memcpy(&out_stream[6], pkt->contents(), pkt->size());

		kSoc->m_crypto.JvEncryptionFast(len, out_stream, out_stream);
	}
	else
	{
		out_stream = new uint8[len];
		out_stream[0] = pkt->GetOpcode();
		if (pkt->size() > 0)
			memcpy(&out_stream[1], pkt->contents(), pkt->size());
	}
	 
	auto buff2 = (const char *)out_stream;
	auto sbuff = (const char *)((const uint8*)"\xaa\x55");
	auto ebuff = (const char *)((const uint8*)"\x55\xaa");
	auto embuff = (const char *)((const uint8*)"\x00");
	std::string sTemp = sbuff;
	std::string eTemp = ebuff;
	std::string mTemp = buff2;
	std::string nTemp = string_format("%d", (uint8*)len);
	  
	return nullptr;
	/*
	//auto _out = (const char*)(const uint8*)"\xaa\x55" + (const uint8*)&len + (const uint8*)out_stream + (const uint8*)"\x55\xaa";
	soc->BurstBegin();
	  
	r = soc->BurstSend((const uint8*)"\xaa\x55", 2);
	if (r) r = soc->BurstSend((const uint8*)&len, 2);
	if (r) r = soc->BurstSend((const uint8*)out_stream, len);
	if (r) r = soc->BurstSend((const uint8*)"\x55\xaa", 2);
	if (r) soc->BurstPush();
	soc->BurstEnd();

	delete[] out_stream;
	return r;*/
}