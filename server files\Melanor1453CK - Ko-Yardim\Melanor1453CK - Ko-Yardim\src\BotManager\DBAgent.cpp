#include "stdafx.h" 
#include "DBAgent.h"
#include "../shared/DateTime.h"

CDBAgent g_DBAgent;

using std::string;
using std::unique_ptr;

CDBAgent::CDBAgent()
{
	m_GameDB = new OdbcConnection();
	m_AccountDB = new OdbcConnection();
}

CDBAgent::~CDBAgent()
{
	delete m_GameDB;
	delete m_AccountDB;
}

bool CDBAgent::Startup(bool bMarsEnabled,  
					   tstring & strGameDSN, tstring & strGameUID, tstring & strGamePWD)
{
	if (!Connect(bMarsEnabled, 
		strGameDSN, strGameUID, strGamePWD))
	{
		// we should probably be a little more specific (i.e. *which* database server)
		printf(_T("ERROR: Failed to connect to the database server."));
		return false;
	}
	  
	return true;
}

bool CDBAgent::Connect(bool bMarsEnabled,  
					   tstring & strGameDSN, tstring & strGameUID, tstring & strGamePWD)
{ 
	if (!m_GameDB->Connect(strGameDSN, strGameUID, strGamePWD, bMarsEnabled))
	{
		ReportSQLError(m_GameDB->GetError());
		return false;
	}

	return true;
}

void CDBAgent::ReportSQLError(OdbcError *pError)
{
	if (pError == nullptr)
		return;

	DateTime time;

	// This is *very* temporary.
	string errorMessage = string_format(_T("[ ODBC Error - %d.%d.%d %d:%d:%d ] ] Source: %s Error: %s Description: %s\n"),
		time.GetDay(),time.GetMonth(),time.GetYear(),time.GetHour(),time.GetMinute(),time.GetSecond(),
		pError->Source.c_str(), pError->ExtendedErrorMessage.c_str(), pError->ErrorMessage.c_str());

	Guard lock(m_lock);
	FILE *fp = fopen("./Logs/GameServer.log", "a");
	if (fp != nullptr)
	{
		fwrite(errorMessage.c_str(), errorMessage.length(), 1, fp);
		fclose(fp);
	}

	TRACE("Database error: %s\n", errorMessage.c_str());
	delete pError;
}
 
  
bool CDBAgent::LoadBotTable(std::vector<_BOT_TYPE> & botList)
{
	unique_ptr<OdbcCommand> dbCommand(m_GameDB->CreateCommand());
	if (dbCommand.get() == nullptr)
		return false;

	string _Auth = "99";
	dbCommand->AddParameter(SQL_PARAM_INPUT, _Auth.c_str(), _Auth.length());

	if (!dbCommand->Execute(_T("SELECT ID,TB_USER.strAccountID,strPasswd,strCharID1 FROM TB_USER INNER JOIN ACCOUNT_CHAR ON TB_USER.strAccountID = ACCOUNT_CHAR.strAccountID WHERE strAuthority = ?")))
	{
		ReportSQLError(m_AccountDB->GetError()); 
	}

	do
	{
		_BOT_TYPE _type;
		if (dbCommand->hasData())
		{
			dbCommand->FetchUInt16(1, _type.ID);
			dbCommand->FetchString(2, _type.strAccountID);
			dbCommand->FetchString(3, _type.strPasswd);
			dbCommand->FetchString(4, _type.strUserID);
		}
		botList.push_back(_type);

	} while (dbCommand->MoveNext()); 

	return true;
}
  